const prisma = require("../database/prisma/getPrismaClient");
const csvtojson = require("csvtojson");
const {
  companySchemaMap,
  amazonSellerSchemaMap,
  prospectSchemaMap,
  amazonProspectSchemaMap,
  matchingSchemaMap,
  transformData,
  applyPrismaTypeCasting,
} = require("./schema");
const {
  companySchema,
  amazonSellerSchema,
  prospectSchema,
  amazonProspectSchema,
  clientProspectMatchSchema,
  validateCompany,
  validateAmazonSeller,
  validateProspect,
  validateAmazonProspect,
  validateMatching,
  validate_update,
} = require("./validator");
const { processSeller } = require("../utils/enhancedSellerGroupUnionFind");

async function processCSV(
  filePath,
  type,
  operation,
  skipErrorRows = false,
  allowBlankRows = false
) {
  const jsonArray = await csvtojson().fromFile(filePath);
  let isValid = [true];
  const processedData = [];
  const dict = {};

  if (type == "amazon_seller" || type == "amazon_prospect") {
    // Load AmazonSeller data for validation
    const sellers = await prisma.amazonSeller.findMany({
      where: {
        website_status: {
          in: ["Final Correct", "Maybe"],
        },
      },
      select: {
        id: true,
        domain: true,
        amazon_seller_id: true,
        marketplace: true,
        seller_group_id: true,
        website_status: true,
      },
    });
    sellers.forEach((seller) => {
      const uniqueKey = `${seller.amazon_seller_id}-${seller.marketplace}`;
      dict[`Stored:-${uniqueKey}`] = seller.id;
      dict[`SellerGroup:-${uniqueKey}`]  = seller.seller_group_id;
      // Seller Group assign based on amazon_seller_id
      dict[`SellerGroup:-${seller.amazon_seller_id}`] = seller.seller_group_id;

      // We should bind with domain that has final correct website status
      if (seller.website_status === "Final Correct") {
        // Order of binding is important here US -> CA -> Other
        dict[`ProspectFlow:-${seller.domain}-${seller.marketplace}`] =
          seller.id;
        // Seller Group assign based on domain
        dict[`SellerGroup:-${seller.domain}`] = seller.seller_group_id;
      }
    });
  } else {
    const companies = await prisma.company.findMany({
      where: {
        website_status: "Final Correct",
      },
      select: {
        id: true,
        domain: true,
        amazon_seller_id: true,
        smartscout_country: true,
      },
    });
    companies.forEach((company) => {
      dict[`Stored:-${company.domain}`] = company.amazon_seller_id;
      dict[`Company:-${company.domain}`] = company.id;
      // console.log(`Stored:-${company.domain}-${company.smartscout_country}`, company.amazon_seller_id);
    });
  }

  for (let i = 0; i < jsonArray.length; i++) {
    let transformedData;
    let schema, validateFunc, schemaMap, modelName;
    switch (type) {
      case "company":
        console.log(`Processing Company row ${i + 1} / ${jsonArray.length}`);
        schema = companySchema;
        validateFunc = validateCompany;
        schemaMap = companySchemaMap;
        break;
      case "amazon_seller":
        console.log(
          `Processing Amazon Seller row ${i + 1} / ${jsonArray.length}`
        );
        schema = amazonSellerSchema;
        validateFunc = validateAmazonSeller;
        schemaMap = amazonSellerSchemaMap;
        modelName = "AmazonSeller";
        break;
      case "prospect":
        console.log(`Processing Prospect row ${i + 1} / ${jsonArray.length}`);
        // console.log(`Transformed Data: ${JSON.stringify(jsonArray[i])}`);
        schema = prospectSchema;
        validateFunc = validateProspect;
        schemaMap = prospectSchemaMap;
        break;
      case "amazon_prospect":
        console.log(
          `Processing Amazon Prospect row ${i + 1} / ${jsonArray.length}`
        );
        schema = amazonProspectSchema;
        validateFunc = validateAmazonProspect;
        schemaMap = amazonProspectSchemaMap;
        modelName = "AmazonProspect";
        break;
      case "matching":
        console.log(
          `Processing Client Prospect Matching row: ${i + 1} / ${jsonArray.length}`
        );
        schema = clientProspectMatchSchema;
        validateFunc = validateMatching;
        schemaMap = matchingSchemaMap;
        break;
      default:
        return [];
    }

    transformedData = await processJsonData(
      jsonArray[i],
      schema,
      isValid,
      schemaMap,
      validateFunc,
      dict,
      modelName,
      type
    );

    if (
      operation === "update" &&
      (type === "company" || type === "amazon_seller")
    ) {
      await validate_update(transformedData, isValid, type);
    }

    if (
      transformedData.errors &&
      transformedData.errors.length > 0 &&
      skipErrorRows
    ) {
      console.log(`Skipping row ${i + 1} due to errors`);
      continue;
    }

    processedData.push(transformedData);
    jsonArray[i].Domain = transformedData.domain;
    jsonArray[i].errors = transformedData.errors;
  }

  if (isValid[0] || skipErrorRows) {
    console.log("Saving data to the database");
    return await saveData(processedData, type, operation, allowBlankRows);
  }

  console.log("Data is not valid for upload");
  return jsonArray;
}

async function saveData(
  processedData,
  type,
  operation,
  allowBlankRows = false
) {
  try {
    let count = 1;
    const totalCount = processedData.length;
    console.log(`Saving ${type} data to the database`);
    console.log(`Total rows: ${totalCount}`);

    switch (type) {
      case "company":
        for (const data of processedData) {
          console.log(`Saving Company rows: ${count} / ${totalCount}`);
          count++;

          delete data.errors;
          let company;
          // Upload data into the Seller Country Matching Table
          if (data.smartscout_country && data.amazon_seller_id) {
            const result = await prisma.sellerCountryMatching.findFirst({
              where: {
                amazon_seller_id: data.amazon_seller_id,
                smartscout_country: data.smartscout_country,
              },
            });

            if (!result) {
              console.log(
                `Creating New Seller Country Matching row for ${data.amazon_seller_id} and ${data.smartscout_country}`
              );
              await prisma.sellerCountryMatching.create({
                data: {
                  amazon_seller_id: data.amazon_seller_id,
                  smartscout_country: data.smartscout_country,
                  seller_url: data.seller_url,
                },
              });
            }
          }
          if (data.company_id) {
            company = await prisma.company.findFirst({
              where: {
                id: data.company_id,
              },
            });
            delete data.company_id;
          }
          // we found the company from the correctness map
          if (company && operation === "update") {
            const existingLookupSources = Array.isArray(company.lookup_sources)
              ? company.lookup_sources
              : [];
            data.lookup_sources = data.lookup_source
              ? [...existingLookupSources, data.lookup_source]
              : existingLookupSources;
            data.lookup_sources = Array.from(new Set(data.lookup_sources));
            await prisma.company.update({
              where: { id: company.id },
              data: data,
            });
          } else {
            // we did not find the company from the company map
            console.log(
              `Searching for company with seller id ${data.amazon_seller_id}`
            );
            company = await prisma.company.findFirst({
              where: {
                amazon_seller_id: data.amazon_seller_id,
              },
            });

            if (company && operation === "update") {
              // we found the company from the conditions
              console.log(
                `Updating Company row with seller id ${data.amazon_seller_id}`
              );
              const existingLookupSources = Array.isArray(
                company.lookup_sources
              )
                ? company.lookup_sources
                : [];
              data.lookup_sources = data.lookup_source
                ? [...existingLookupSources, data.lookup_source]
                : existingLookupSources;
              data.lookup_sources = Array.from(new Set(data.lookup_sources));
              data.smartscout_country = company.smartscout_country;

              // Clean data object
              Object.keys(data).forEach((key) => {
                if (allowBlankRows) {
                  return;
                }
                if (typeof data[key] === "string") {
                  data[key] = data[key].trim();
                }
                if (
                  data[key] === undefined ||
                  data[key] === "" ||
                  data[key] === null
                ) {
                  delete data[key];
                }
              });

              await prisma.company.update({
                where: { id: company.id },
                data: data,
              });
            } else if (!company && operation === "insert") {
              console.log(
                `Creating New Company row with seller id ${data.amazon_seller_id}`
              );
              data.lookup_sources = data.lookup_source
                ? [data.lookup_source]
                : [];
              console.log("Creating Company row:");
              await prisma.company.create({
                data: data,
              });
            }
          }
        }
        break;

      case "amazon_seller":
        for (const data of processedData) {
          console.log(`Upserting Amazon Seller rows: ${count} / ${totalCount}`);
          count++;

          delete data.errors;

          // Get existing seller for lookup sources merging
          const existingSeller = await prisma.amazonSeller.findFirst({
            where: {
              amazon_seller_id: data.amazon_seller_id,
              marketplace: data.marketplace,
            },
            select: {
              id: true,
              lookup_sources: true,
              seller_group_id: true
            }
          });

          // Handle lookup sources merging
          let mergedLookupSources = [];
          if (existingSeller) {
            const existingLookupSources = Array.isArray(existingSeller.lookup_sources)
              ? existingSeller.lookup_sources
              : [];
            mergedLookupSources = data.lookup_source
              ? [...existingLookupSources, data.lookup_source]
              : existingLookupSources;
            mergedLookupSources = Array.from(new Set(mergedLookupSources));
          } else {
            mergedLookupSources = data.lookup_source ? [data.lookup_source] : [];
          }
          data.lookup_sources = mergedLookupSources;

          // Create seller group if needed
          if (!data.seller_group_id && !existingSeller?.seller_group_id) {
            const newGroup = await prisma.sellerGroup.create({
              data: {
                name: `Group-${data.amazon_seller_id}` || `Group-${data.domain}`,
                seller_ids: [data.amazon_seller_id],
                domains: data.domain ? [data.domain] : []
              },
            });
            data.seller_group_id = newGroup.id;
          } else if (!data.seller_group_id && existingSeller?.seller_group_id) {
            // Use existing seller group if not provided
            data.seller_group_id = existingSeller.seller_group_id;
          }

          // Clean data object for upsert
          if (!allowBlankRows) {
            Object.keys(data).forEach((key) => {
              if (typeof data[key] === "string") {
                data[key] = data[key].trim();
              }
              if (
                data[key] === undefined ||
                data[key] === "" ||
                data[key] === null
              ) {
                delete data[key];
              }
            });
          }

          // Prepare data for upsert (remove lookup_source as it's processed)
          const { lookup_source, ...upsertData } = data;

          // Perform upsert operation
          try {
            let savedSeller = null
            if (existingSeller) {
              console.log(`Updating Amazon Seller ${existingSeller.id} (${data.amazon_seller_id} - ${data.marketplace})`);
              savedSeller = await prisma.amazonSeller.update({
                where: { id: existingSeller.id },
                data: data
              });
            } else {
              console.log(`Creating Amazon Seller ${data.amazon_seller_id} - ${data.marketplace}`);
              savedSeller = await prisma.amazonSeller.create({
                data: data,
              });
            }

            // Process seller for union-find grouping after upsert
            try {
              console.log(`Processing seller groups for ${savedSeller.amazon_seller_id}...`);
              const processingResult = await processSeller(savedSeller);
              console.log(`Seller processing result: ${processingResult.action} (created: ${processingResult.created.join(', ') || 'none'})`);
              
              if (processingResult.merged) {
                console.log(`Groups merged during processing of seller ${savedSeller.amazon_seller_id}`);
              }
            } catch (processingError) {
              console.error(`Error processing seller groups for ${savedSeller.amazon_seller_id}:`, processingError.message);
              // Don't fail the CSV processing for seller processing errors
            }

          } catch (upsertError) {
            console.error(`Error upserting Amazon Seller ${data.amazon_seller_id} - ${data.marketplace}:`, upsertError.message);
            throw upsertError; // Re-throw to maintain error handling flow
          }
        }
        break;

      case "prospect":
        for (const data of processedData) {
          console.log(`Saving prospect rows: ${count} / ${totalCount}`);
          count++;
          if (!data.amazon_seller_id && !data.company_id) {
            console.log(
              "Skipping Prospect row as it cannot be mapped to a Company"
            );
            continue;
          }
          delete data.errors;
          const conditions = [];
          if (
            data.person_linkedin &&
            data.person_linkedin.includes("linkedin")
          ) {
            conditions.push({ person_linkedin: data.person_linkedin });
          }
          if (data.phone && data.phone.trim() !== "") {
            conditions.push({ phone: data.phone });
          }

          if (data.email && data.email.trim() !== "") {
            conditions.push({ email: data.email });
          }

          if (conditions.length > 0) {
            let result;
            result = await prisma.prospect.findFirst({
              where: { OR: conditions },
            });

            if (result) {
              const existingSources = Array.isArray(result.sources)
                ? result.sources
                : [];

              data.sources = data.source
                ? [...existingSources, data.source]
                : existingSources;

              data.sources = Array.from(new Set(data.sources));

              // }

              console.log("Updating Prospect row:");
              await prisma.prospect.update({
                where: { prospect_id: result.prospect_id },
                data: data,
              });
            } else {
              data.sources = data.source ? [data.source] : [];
              // TODO: Remove this after in the new data release
              // data.seller_group_id = 1;
              console.log("Creating Prospect row:");
              result = await prisma.prospect.create({
                data: data,
              });
            }

            if (data.company_id && data.source) {
              console.log(
                `Adding lookup source to seller ${data.amazon_seller_id} :- ${data.source}`
              );
              const company = await prisma.company.findFirst({
                where: {
                  id: data.company_id,
                },
              });
              const existingLookupSources = Array.isArray(
                company.lookup_sources
              )
                ? company.lookup_sources
                : [];
              company.lookup_sources = existingLookupSources
                ? [...existingLookupSources, data.source]
                : existingLookupSources;
              company.lookup_sources = Array.from(
                new Set(company.lookup_sources)
              );
              await prisma.company.update({
                where: { id: company.id },
                data: company,
              });

              // Check if the prospect has a seller_group_id
              const currentGroupId =
                (result && result?.seller_group_id) || data?.seller_group_id;
              // if (currentGroupId) {
              //   console.log(
              //     `Updating all Amazon Sellers in group ${currentGroupId} with lookup source ${data.source}`
              //   );

              //   // Find all Amazon sellers in the same group
              //   const groupSellers = await prisma.amazonSeller.findMany({
              //     where: {
              //       seller_group_id: currentGroupId,
              //     },
              //   });

              //   // Update each Amazon seller with the lookup source
              //   for (const seller of groupSellers) {
              //     console.log(
              //       `Updating Amazon Seller ${seller.id} with lookup source ${data.source}`
              //     );

              //     // Handle lookup sources for the seller
              //     const sellerLookupSources = Array.isArray(
              //       seller.lookup_sources
              //     )
              //       ? seller.lookup_sources
              //       : [];

              //     const updatedLookupSources = data.source
              //       ? [...sellerLookupSources, data.source]
              //       : sellerLookupSources;

              //     // Remove duplicates
              //     const uniqueLookupSources = Array.from(
              //       new Set(updatedLookupSources)
              //     );

              //     // Update the seller
              //     await prisma.amazonSeller.update({
              //       where: { id: seller.id },
              //       data: {
              //         lookup_sources: uniqueLookupSources,
              //       },
              //     });
              //   }
              // }
            }
          } else if (conditions.length == 0) {
            console.log("There are no conditions to match the prospect");
          }
        }
        break;

      case "amazon_prospect":
        for (const data of processedData) {
          console.log(`Saving Amazon Prospect rows: ${count} / ${totalCount}`);
          count++;

          delete data.errors;

          if (!data.seller_id) {
            console.log(
              "Skipping Amazon Prospect row as it requires a Seller ID"
            );
            continue;
          }

          // Create a clean prospect data object with only valid model fields
          const prospectData = {
            person_name: data.person_name || "",
            person_linkedin: data.person_linkedin || "",
            email: data.email || "",
            job_title: data.job_title || "",
            source: data.source || "",
            email_status: data.email_status || "",

            seller_id: data.seller_id || null,
            sources: data.sources || [],
          };

          // Search condition based on available fields
          const conditions = [];
          if (prospectData.person_linkedin && prospectData.person_linkedin.includes("linkedin")) {
            conditions.push({ person_linkedin: prospectData.person_linkedin });
          }

          if (prospectData.email && prospectData.email.length > 3) {
            conditions.push({ email: prospectData.email });
          }

          if (conditions.length > 0) {
            let currentProspect;
            currentProspect = await prisma.amazonProspect.findFirst({
              where: { OR: conditions },
            });

            if (currentProspect) {
              // Update existing prospect
              const existingSources = Array.isArray(currentProspect.sources)
                ? currentProspect.sources
                : [];

              prospectData.sources = prospectData.source
                ? [...existingSources, prospectData.source]
                : existingSources;

              prospectData.sources = Array.from(new Set(prospectData.sources));

              console.log("Updating Amazon Prospect row:");
              await prisma.amazonProspect.update({
                where: { prospect_id: currentProspect.prospect_id },
                data: prospectData,
              });
            } else {
              // Create new prospect
              prospectData.sources = prospectData.source
                ? [prospectData.source]
                : [];

              console.log("Creating Amazon Prospect row:");
              await prisma.amazonProspect.create({
                data: prospectData,
              });
            }

            // Update Amazon Seller with the lookup source
            if (prospectData.source && prospectData.seller_id) {
              const currentSellerId = prospectData.seller_id;

              console.log(
                `Updating all Amazon Sellers in seller ${currentSellerId} with lookup source ${prospectData.source}`
              );

              // Find all Amazon sellers in the same group
              const currentSeller = await prisma.amazonSeller.findFirst({
                where: {
                  id: currentSellerId,
                },
              });

              // Update each Amazon seller with the lookup source
              console.log(
                `Updating Amazon Seller ${currentSellerId} with lookup source ${prospectData.source}`
              );

              // Handle lookup sources for the seller
              const sellerLookupSources = Array.isArray(
                currentSeller.lookup_sources
              )
                ? currentSeller.lookup_sources
                : [];

              const updatedLookupSources = prospectData.source
                ? [...sellerLookupSources, prospectData.source]
                : sellerLookupSources;

              // Remove duplicates
              const uniqueLookupSources = Array.from(
                new Set(updatedLookupSources)
              );

              // Update the seller
              await prisma.amazonSeller.update({
                where: { id: currentSellerId },
                data: {
                  lookup_sources: uniqueLookupSources,
                },
              });
            }
          } else if (conditions.length == 0) {
            console.log(
              "There are no conditions to match the Amazon prospect - requires either email or LinkedIn"
            );
          }
        }
        break;

      case "matching":
        for (const data of processedData) {
          console.log(
            `Updating Client Prospect Matching rows: ${count} / ${totalCount}`
          );
          count++;

          delete data.errors;
          try {
            const result = await prisma.clientsProspectsMatching.findFirst({
              where: {
                client: data.client,
                prospect_id: data.prospect_id,
              },
            });

            if (result) {
              console.log("Updating Client Prospect Matching row:");
              await prisma.clientsProspectsMatching.update({
                where: {
                  match_id: result.match_id,
                },
                data: {
                  jeff_output_status: data.jeff_output_status,
                  date_reached_out: data.date_reached_out,
                },
              });
            } else {
              console.log("Creating Client Prospect Matching row:");
              await prisma.clientsProspectsMatching.create({
                data: {
                  client: data.client,
                  prospect_id: data.prospect_id,
                  jeff_output_status: data.jeff_output_status,
                  date_reached_out: data.date_reached_out,
                },
              });
            }
          } catch (error) {
            console.error(`Error processing row ${count}:`, error);
          }
        }
        break;
      default:
        throw new Error("Invalid type parameter");
    }

    return [];
  } catch (err) {
    console.error(err);
    return [err.message];
  }
}
// Only attach Model name where Strict type casting is required
async function processJsonData(
  data,
  schema,
  isValid,
  schemaMap,
  validate_func,
  dict,
  modelName,
  type
) {
  // Transform the data according to schema
  const transformedData = await transformData(data, schemaMap, modelName, type);
  console.log("Transformed Data", transformedData);

  // Validate the transformed data
  await validate_func(transformedData, isValid, dict);

  // Check for validation errors
  const { error } = schema.validate(transformedData, { abortEarly: false });
  if (error) {
    isValid[0] = false;

    const errors = error.details.map((detail) => {
      const value = detail.context.value;
      return `${detail.message
        .replace(/"/g, "")
        .replace(/\\/g, "")}. Value: ${value}`;
    });
    transformedData.errors = transformedData.errors
      ? [...transformedData.errors, ...errors]
      : errors;
  }

  // Return the transformed data (with errors if any)
  return transformedData;
}

module.exports = { processCSV };
