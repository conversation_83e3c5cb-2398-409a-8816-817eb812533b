{"name": "sellerbot", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "migrate:sqlite": "npx prisma migrate dev --schema /database/prisma/sqlitePrisma/sqlite.schema.prisma", "start-leads": "node ./services/generateLeads/workers/mainWorker.js", "dev": "nodemon index.js", "start": "npm run generate-docs && node index.js && pnpm sentry:sourcemaps", "start-serp-worker": "node ./services/generateLeads/workers/serpWorker/index.js", "start-openai-worker": "node ./services/generateLeads/workers/openaiWorker/index.js", "debug:scrape": "DEBUG=\"worker-pool:*\" node ./scrapeAmazon/test.js", "generate-docs": "node swagger.js", "debug": "DEBUG=\"worker-pool:*\" nodemon index.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org brand-buddy --project node node_modules && sentry-cli sourcemaps upload --org brand-buddy --project node node_modules", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\""}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.808.0", "@google-cloud/local-auth": "^3.0.1", "@prisma/client": "^6.5.0", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "bezier-js": "^6.1.4", "blessed": "^0.1.81", "bottleneck": "^2.19.5", "cheerio": "^1.0.0", "cli-table": "^0.3.11", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.0", "express-session": "^1.18.0", "express-swagger-generator": "^1.1.17", "fast-csv": "^5.0.2", "fast-fuzzy": "^1.12.0", "flat-cache": "^6.1.5", "fs": "^0.0.1-security", "fuse.js": "^7.1.0", "googleapis": "^144.0.0", "inquirer": "^12.0.0", "joi": "^17.13.3", "json-2-csv": "^5.5.9", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mailparser": "^3.7.1", "multer": "^1.4.5-lts.1", "newrelic": "^12.14.0", "node-cron": "^3.0.3", "nodemon": "^3.1.4", "openai": "^4.77.0", "papaparse": "^5.4.1", "path": "^0.12.7", "pg": "^8.14.1", "prisma": "^6.5.0", "prompts": "^2.4.2", "puppeteer": "^23.11.1", "sqlite3": "^5.1.7", "string-similarity": "^4.0.4", "swagger-autogen": "^2.23.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-dist": "^5.20.4", "swagger-ui-express": "^5.0.1", "tldts": "^7.0.11", "whatwg-url": "^14.1.0"}, "prisma": {"schema": "./database/prisma/schema.prisma"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.7", "prettier": "^3.5.2"}}