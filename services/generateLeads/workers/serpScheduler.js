const { processSerpRequests } = require("./serpWorker/serpWorker");
const { processSyncRequests } = require("./serpWorker/serpSyncWorker");
const { processOpenAIRequests } = require("./openaiWorker/openaiWorker");

async function runSerpWorkers() {
  console.log("Starting SERP workers...");

  // Run both workers every 10 seconds
  setInterval(async () => {
    try {
      // Run async worker
      const syncResults = await processSyncRequests();
      const asyncResults = await processSerpRequests();
      if (asyncResults) {
        console.log("Async SERP batch processed:", asyncResults);
      }

      // Run sync worker
      if (syncResults) {
        console.log("Sync SERP batch processed:", syncResults);
      }
    } catch (error) {
      console.error("SERP processing error:", error);
    }
  }, 10000); // 10 seconds
}

async function runOpenAIWorkers() {
  console.log("Starting OpenAI workers...");

  // Run OpenAI worker every 15 seconds
  setInterval(async () => {
    try {
      const openaiResults = await processOpenAIRequests();
      if (openaiResults) {
        console.log("OpenAI batch processed:", openaiResults);
      }
    } catch (error) {
      console.error("OpenAI processing error:", error);
    }
  }, 15000); // 15 seconds
}

async function runAllWorkers() {
  console.log("Starting all workers...");

  // Start SERP workers
  runSerpWorkers();

  // Start OpenAI workers
  runOpenAIWorkers();
}

// Export for use in main application
module.exports = {
  runSerpWorkers,
  runOpenAIWorkers,
  runAllWorkers,
};
