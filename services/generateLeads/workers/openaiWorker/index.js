const newrelic = require("newrelic");
const { processOpenAIRequests } = require("./openaiWorker");

async function runWorker() {
  return newrelic.startBackgroundTransaction('processOpenAITasks', 'SellerBotOpenAIWorker', async function() {
    const transaction = newrelic.getTransaction();
    
    try {
      // Process OpenAI requests
      const result = await processOpenAIRequests();
      
      if (result) {
        console.log("OpenAI batch processed:", result);
      }
      
      return result;
    } catch (error) {
      console.error("Error in OpenAI worker:", error);
      throw error;
    } finally {
      transaction.end();
    }
  });
}

// Start the worker loop
function startWorker() {
  console.log("Starting OpenAI worker...");

  // Run immediately on startup
  runWorker();

  // Then run every 30 seconds
  setInterval(runWorker, 30000);
}

// Export for use in main application
module.exports = {
  runWorker,
  startWorker,
};

// If this file is run directly, start the worker
if (require.main === module) {
  startWorker();
}
