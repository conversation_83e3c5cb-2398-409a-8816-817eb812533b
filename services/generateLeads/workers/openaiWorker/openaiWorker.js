const prisma = require("../../../../database/prisma/getPrismaClient");
const { processOpenAIBatch } = require("../../OpenAI/getOpenAIRequest");

const WORKER_PROCESS_ID = `openai_worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
const BATCH_SIZE = 10; // Process 10 leads at a time

/**
 * Process OpenAI requests for pending leads
 */
async function processOpenAIRequests() {
  try {
    console.log("Starting OpenAI worker...");

    // Find active OpenAI jobs
    const activeOpenAIJobs = await prisma.leadJob.findMany({
      where: {
        mode: "openai",
        status: "pending",
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    if (activeOpenAIJobs.length === 0) {
      console.log("No active OpenAI jobs found");
      return null;
    }

    console.log(`Found ${activeOpenAIJobs.length} active OpenAI jobs`);

    const activeJobIds = activeOpenAIJobs.map((job) => job.id);

    // Find leads that need OpenAI processing
    const pendingLeads = await prisma.lead.findMany({
      where: {
        jobId: { in: activeJobIds },
        status: "pending",
        mode: "openai",
      },
      take: BATCH_SIZE,
      orderBy: {
        createdAt: "asc",
      },
    });

    if (pendingLeads.length === 0) {
      console.log("No pending OpenAI leads found");
      return null;
    }

    console.log(`Processing ${pendingLeads.length} OpenAI leads`);

    // Update leads to processing status
    await prisma.lead.updateMany({
      where: {
        id: { in: pendingLeads.map(lead => lead.id) },
      },
      data: {
        status: "openai_requested",
        processId: WORKER_PROCESS_ID,
      },
    });

    // Get custom prompt from job if available
    const job = activeOpenAIJobs.find(j => j.id === pendingLeads[0].jobId);
    const customPrompt = job?.resultJson?.customPrompt || null;

    // Process the batch
    const results = await processOpenAIBatch(pendingLeads, customPrompt);

    console.log(`Completed processing ${results.length} OpenAI leads`);

    // Check if job is complete
    for (const job of activeOpenAIJobs) {
      const remainingLeads = await prisma.lead.count({
        where: {
          jobId: job.id,
          status: { in: ["pending", "openai_requested"] },
        },
      });

      if (remainingLeads === 0) {
        // Job is complete, update status
        await prisma.leadJob.update({
          where: { id: job.id },
          data: { status: "completed" },
        });
        console.log(`OpenAI job ${job.id} completed`);
      }
    }

    return {
      processedLeads: results.length,
      jobIds: activeJobIds,
      workerId: WORKER_PROCESS_ID,
    };

  } catch (error) {
    console.error("Error in OpenAI worker:", error);
    
    // Reset any leads that were being processed by this worker
    await prisma.lead.updateMany({
      where: {
        processId: WORKER_PROCESS_ID,
        status: "openai_requested",
      },
      data: {
        status: "pending",
        processId: null,
      },
    });

    throw error;
  }
}

/**
 * Process a specific OpenAI job by ID
 */
async function processOpenAIJob(jobId) {
  try {
    const job = await prisma.leadJob.findUnique({
      where: { id: jobId },
      include: { leads: true },
    });

    if (!job) {
      throw new Error(`OpenAI job ${jobId} not found`);
    }

    if (job.mode !== "openai") {
      throw new Error(`Job ${jobId} is not an OpenAI job`);
    }

    const pendingLeads = job.leads.filter(lead => lead.status === "pending");

    if (pendingLeads.length === 0) {
      console.log(`No pending leads for OpenAI job ${jobId}`);
      return null;
    }

    console.log(`Processing ${pendingLeads.length} leads for OpenAI job ${jobId}`);

    // Update job status to processing
    await prisma.leadJob.update({
      where: { id: jobId },
      data: { status: "processing" },
    });

    // Get custom prompt from job
    const customPrompt = job.resultJson?.customPrompt || null;

    // Process leads in batches
    const results = [];
    for (let i = 0; i < pendingLeads.length; i += BATCH_SIZE) {
      const batch = pendingLeads.slice(i, i + BATCH_SIZE);
      
      // Update batch to processing
      await prisma.lead.updateMany({
        where: {
          id: { in: batch.map(lead => lead.id) },
        },
        data: {
          status: "openai_requested",
          processId: WORKER_PROCESS_ID,
        },
      });

      const batchResults = await processOpenAIBatch(batch, customPrompt);
      results.push(...batchResults);
    }

    // Update job status to completed
    await prisma.leadJob.update({
      where: { id: jobId },
      data: { status: "completed" },
    });

    console.log(`OpenAI job ${jobId} completed with ${results.length} processed leads`);

    return {
      jobId: jobId,
      processedLeads: results.length,
      results: results,
    };

  } catch (error) {
    console.error(`Error processing OpenAI job ${jobId}:`, error);
    
    // Update job status to failed
    await prisma.leadJob.update({
      where: { id: jobId },
      data: { status: "failed" },
    });

    throw error;
  }
}

/**
 * Get OpenAI worker statistics
 */
async function getOpenAIWorkerStats() {
  const stats = await prisma.openAI_Usage.aggregate({
    _count: { id: true },
    _sum: {
      inputTokens: true,
      outputTokens: true,
      totalCost: true,
    },
    _avg: {
      totalCost: true,
    },
  });

  const successCount = await prisma.openAI_Usage.count({
    where: { success: true },
  });

  const failureCount = await prisma.openAI_Usage.count({
    where: { success: false },
  });

  return {
    totalRequests: stats._count.id || 0,
    totalInputTokens: stats._sum.inputTokens || 0,
    totalOutputTokens: stats._sum.outputTokens || 0,
    totalCost: stats._sum.totalCost || 0,
    averageCost: stats._avg.totalCost || 0,
    successCount: successCount,
    failureCount: failureCount,
    successRate: stats._count.id > 0 ? (successCount / stats._count.id) * 100 : 0,
  };
}

module.exports = {
  processOpenAIRequests,
  processOpenAIJob,
  getOpenAIWorkerStats,
  WORKER_PROCESS_ID,
};
